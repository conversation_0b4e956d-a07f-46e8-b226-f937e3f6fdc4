import { EngagementData } from '../types'
import { endpoints } from '../config/environment'

export class PollingService {
  private intervalId: number | null = null
  private onDataCallback?: (data: EngagementData) => void
  private sessionId: number | null = null
  private isActive: boolean = false
  private pollInterval: number = 1000 // Poll every 1 second

  connect(onData: (data: EngagementData) => void, sessionId: number) {
    this.onDataCallback = onData
    this.sessionId = sessionId
    this.isActive = true

    console.log(`Starting polling for session ${sessionId}`)

    // Start polling for engagement data
    this.intervalId = window.setInterval(() => {
      this.pollForData()
    }, this.pollInterval)
  }

  private async pollForData() {
    if (!this.isActive || !this.sessionId) return

    try {
      // Use the new live-data endpoint
      const response = await fetch(`${endpoints.api}/sessions/${this.sessionId}/live-data`)
      if (response.ok) {
        const data = await response.json()
        if (data && data.session_id && this.onDataCallback) {
          this.onDataCallback(data)
        }
      }
    } catch (error) {
      console.error('Error polling for data:', error)
    }
  }

  disconnect() {
    this.isActive = false
    if (this.intervalId) {
      clearInterval(this.intervalId)
      this.intervalId = null
    }
    console.log('Polling service disconnected')
  }

  isConnected(): boolean {
    return this.isActive && this.intervalId !== null
  }
}
