package handlers

import (
	"ctrl-vibe-backend/internal/models"
	"ctrl-vibe-backend/pkg/database"
	"net/http"
	"os"
	"strconv"
	"time"
	"github.com/gin-gonic/gin"
)

// In-memory storage for development mode
var (
	memorySessions = make(map[uint]models.Session)
	nextSessionID uint = 1
)

func CreateSession(c *gin.Context) {
	var session models.Session
	if err := c.ShouldBindJSON(&session); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	session.StartTime = time.Now()
	session.IsActive = true

	// Use database if available, otherwise use memory
	if os.Getenv("DISABLE_DATABASE") == "true" || database.DB == nil {
		session.ID = nextSessionID
		nextSessionID++
		memorySessions[session.ID] = session
	} else {
		if err := database.DB.Create(&session).Error; err != nil {
			c.<PERSON>(http.StatusInternalServerError, gin.H{"error": "Failed to create session"})
			return
		}
	}

	c.<PERSON>(http.StatusCreated, session)
}

func GetSessions(c *gin.Context) {
	if os.Getenv("DISABLE_DATABASE") == "true" || database.DB == nil {
		var sessions []models.Session
		for _, session := range memorySessions {
			sessions = append(sessions, session)
		}
		c.JSON(http.StatusOK, sessions)
		return
	}

	var sessions []models.Session
	if err := database.DB.Preload("Metrics").Preload("Alerts").Find(&sessions).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch sessions"})
		return
	}

	c.JSON(http.StatusOK, sessions)
}

func GetSession(c *gin.Context) {
	id := c.Param("id")
	var session models.Session
	
	if err := database.DB.Preload("Metrics").Preload("Alerts").First(&session, id).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Session not found"})
		return
	}

	c.JSON(http.StatusOK, session)
}

func EndSession(c *gin.Context) {
	id := c.Param("id")
	
	if os.Getenv("DISABLE_DATABASE") == "true" || database.DB == nil {
		sessionIDInt, err := strconv.Atoi(id)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid session ID"})
			return
		}
		sessionID := uint(sessionIDInt)
		
		session, exists := memorySessions[sessionID]
		if !exists {
			c.JSON(http.StatusNotFound, gin.H{"error": "Session not found"})
			return
		}
		
		now := time.Now()
		session.EndTime = &now
		session.IsActive = false
		memorySessions[sessionID] = session
		
		c.JSON(http.StatusOK, session)
		return
	}

	var session models.Session
	if err := database.DB.First(&session, id).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Session not found"})
		return
	}

	now := time.Now()
	session.EndTime = &now
	session.IsActive = false

	if err := database.DB.Save(&session).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to end session"})
		return
	}

	c.JSON(http.StatusOK, session)
}
