{"version": 3, "names": ["_assert<PERSON>lassBrand", "require", "_classPrivateMethodGet", "receiver", "privateSet", "fn", "assertClassBrand"], "sources": ["../../src/helpers/classPrivateMethodGet.js"], "sourcesContent": ["/* @minVersion 7.1.6 */\n/* @onlyBabel7 */\n\nimport assertClassBrand from \"assertClassBrand\";\nexport default function _classPrivateMethodGet(receiver, privateSet, fn) {\n  assertClassBrand(privateSet, receiver);\n  return fn;\n}\n"], "mappings": ";;;;;;AAGA,IAAAA,iBAAA,GAAAC,OAAA;AACe,SAASC,sBAAsBA,CAACC,QAAQ,EAAEC,UAAU,EAAEC,EAAE,EAAE;EACvEC,iBAAgB,CAACF,UAAU,EAAED,QAAQ,CAAC;EACtC,OAAOE,EAAE;AACX", "ignoreList": []}