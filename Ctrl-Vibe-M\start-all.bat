@echo off
echo ========================================
echo    🚀 CTRL-VIBE ALL-IN-ONE STARTUP
echo ========================================
echo.

REM Kill any existing processes on our ports
echo [CLEANUP] Stopping existing processes...
for /f "tokens=5" %%a in ('netstat -aon ^| findstr :5173') do taskkill /f /pid %%a >nul 2>&1
for /f "tokens=5" %%a in ('netstat -aon ^| findstr :8080') do taskkill /f /pid %%a >nul 2>&1
for /f "tokens=5" %%a in ('netstat -aon ^| findstr :8001') do taskkill /f /pid %%a >nul 2>&1
echo ✅ Cleanup complete

echo.
echo [STARTING] Launching all services simultaneously...
echo.

REM Start Python Backend (replaces Go backend)
echo 🐍 Starting Python Backend on port 8080...
start "🐍 Python Backend" cmd /k "cd backend-python && pip install flask flask-cors flask-socketio requests python-socketio eventlet && python app.py"

REM Wait 3 seconds for backend to initialize
timeout /t 3 /nobreak >nul

REM Start AI Engine
echo 🤖 Starting AI Engine on port 8001...
start "🤖 AI Engine" cmd /k "cd ai-engine && pip install opencv-python flask requests numpy && python src/main_simple.py"

REM Wait 2 seconds for AI engine to initialize
timeout /t 2 /nobreak >nul

REM Start Frontend
echo ⚛️ Starting React Frontend on port 5173...
start "⚛️ React Frontend" cmd /k "cd frontend && npm install && npm run dev"

echo.
echo ========================================
echo    ✨ ALL SERVICES LAUNCHING...
echo ========================================
echo.
echo 🌐 Services will be available at:
echo   • Frontend Dashboard: http://localhost:5173
echo   • Python Backend API: http://localhost:8080  
echo   • AI Engine: http://localhost:8001
echo.
echo 📋 WORKFLOW:
echo   1. Wait ~30 seconds for all services to start
echo   2. Open: http://localhost:5173
echo   3. Click "Grant Camera Permission"
echo   4. Click "Start New Session"
echo   5. Click "Start Camera" to begin AI analysis
echo.
echo 💡 All services run in separate windows - you can monitor each one
echo 🛑 Close any window to stop that service
echo.
echo ⏳ Please wait for all services to fully load...
pause
