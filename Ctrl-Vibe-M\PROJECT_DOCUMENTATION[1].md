# 📋 **Ctrl-Vibe Project Context & Documentation**

## 🎯 **Project Overview**
**Ctrl-Vibe** is a real-time AI-powered classroom engagement analytics system with a retro-cyberpunk UI. It analyzes student engagement through computer vision and provides live feedback to educators.

## 🏗️ **System Architecture**

### **3-Tier Architecture:**
```
Frontend (React) ←→ Backend (Go) ←→ AI Engine (Python)
     ↓                    ↓                ↓
  WebSocket         REST API + Redis    OpenCV + Flask
```

## 🔄 **Data Flow Pipeline**

### **1. Session Initialization**
```
Frontend → POST /api/sessions → Backend → Creates Session → Returns Session ID
```

### **2. Camera Activation**
```
Frontend → POST /api/sessions/{id}/start-camera → Backend → HTTP POST localhost:8001/start → AI Engine
```

### **3. Frame Processing Pipeline**
```
Frontend Camera → Captures Frame → POST /api/sessions/{id}/process-frame → Backend → 
HTTP POST localhost:8001/process → AI Engine → OpenCV Face Detection → 
Engagement Analysis → POST /api/ai/data → Backend → Redis Cache → 
WebSocket Broadcast → Frontend Live Updates
```

### **4. Session Termination**
```
Frontend → POST /api/sessions/{id}/stop-camera → Backend → HTTP POST localhost:8001/stop → AI Engine
Frontend → PUT /api/sessions/{id}/end → Backend → Updates Session Status
```

## 📁 **Project Structure**

```
ctrl-vibe-migrated/
├── frontend/                    # React + TypeScript + TailwindCSS
│   ├── src/
│   │   ├── components/shared/   # CameraFeed, CameraControl
│   │   ├── pages/Dashboard.tsx  # Main dashboard
│   │   ├── services/           # API & WebSocket clients
│   │   └── types/              # TypeScript definitions
│   └── package.json
├── backend-go/                  # Go REST API + WebSocket
│   ├── cmd/server/main.go      # Entry point
│   ├── internal/
│   │   ├── handlers/           # HTTP route handlers
│   │   ├── models/             # Data models
│   │   └── websocket/          # WebSocket hub
│   ├── pkg/
│   │   ├── cache/              # Redis integration
│   │   └── database/           # PostgreSQL integration
│   └── go.mod
├── ai-engine/                   # Python AI processing
│   ├── src/main_simple.py      # Flask server + OpenCV
│   └── requirements.txt
├── docker-compose.dev.yml       # PostgreSQL + Redis
└── start.bat                    # Deployment script
```

## 🔧 **Technology Stack**

### **Frontend:**
- **React 18** + TypeScript
- **TailwindCSS** with custom cyberpunk theme
- **WebSocket** for real-time updates
- **Vite** for development server

### **Backend:**
- **Go 1.21** with Gin framework
- **PostgreSQL** for persistent storage
- **Redis** for caching (5-minute TTL)
- **WebSocket** for live broadcasting
- **CORS** enabled for cross-origin requests

### **AI Engine:**
- **Python 3.9** with Flask
- **OpenCV** for computer vision
- **NumPy** for array processing
- **Haar Cascade** for face detection

### **Infrastructure:**
- **Docker** for PostgreSQL + Redis
- **HTTP** communication between services
- **JSON** data format

## 🚀 **Key Features**

### **Real-time Processing:**
- Live camera feed with AI overlays
- WebSocket-based instant updates
- 30 FPS frame processing capability
- Redis caching for performance

### **AI Analytics:**
- Face detection and counting
- Engagement level classification (high/medium/low)
- Engagement score calculation (0-100%)
- Real-time metrics broadcasting

### **UI/UX:**
- Retro-cyberpunk design theme
- Pixelated fonts (Press Start 2P, VT323)
- Glass morphism effects
- Responsive design

## 🔄 **Service Communication**

### **Port Configuration:**
- **Frontend**: `localhost:5173` (Vite dev server)
- **Backend**: `localhost:8080` (Go Gin server)
- **AI Engine**: `localhost:8001` (Flask server)
- **PostgreSQL**: `localhost:5432`
- **Redis**: `localhost:6379`

### **API Endpoints:**
```
POST   /api/sessions                    # Create session
GET    /api/sessions                    # List sessions
PUT    /api/sessions/{id}/end           # End session
POST   /api/sessions/{id}/start-camera  # Start AI processing
POST   /api/sessions/{id}/stop-camera   # Stop AI processing
POST   /api/sessions/{id}/process-frame # Send frame to AI
POST   /api/ai/data                     # Receive AI results
GET    /ws                              # WebSocket connection
```

## 🎮 **User Workflow**

1. **Start System**: Run `start.bat` → All services launch
2. **Open Dashboard**: Navigate to `localhost:5173`
3. **Create Session**: Click "Start New Session"
4. **Activate Camera**: Click "Start Camera" → AI begins processing
5. **Live Analysis**: View real-time engagement metrics
6. **Stop Session**: Click "Stop Camera" → End analysis

## 🔧 **Development Mode**

### **Database Fallback:**
- Uses in-memory storage if PostgreSQL unavailable
- Redis operations gracefully skip if unavailable
- Environment variable `DISABLE_DATABASE=true` for dev mode

### **Error Handling:**
- Camera access conflicts resolved
- Frame processing error recovery
- WebSocket reconnection logic
- Graceful service degradation

## 📊 **Data Models**

### **Session:**
```go
type Session struct {
    ID        uint      `json:"id"`
    StartTime time.Time `json:"start_time"`
    EndTime   *time.Time `json:"end_time"`
    IsActive  bool      `json:"is_active"`
}
```

### **AI Data:**
```json
{
  "session_id": 1,
  "timestamp": 1643723400,
  "face_count": 3,
  "overall_engagement_score": 75.5,
  "engagement_level": "high",
  "audio_level": 0.5,
  "gesture_count": 3,
  "alerts": []
}
```

## 🚀 **Deployment**

### **Quick Start:**
```bash
# Clone and run
git clone <repo>
cd ctrl-vibe-migrated
start.bat
```

### **Manual Start:**
```bash
# Start infrastructure
docker-compose -f docker-compose.dev.yml up -d

# Start backend
cd backend-go && go run cmd/server/main.go

# Start AI engine
cd ai-engine && py src/main_simple.py

# Start frontend
cd frontend && npm run dev
```

## 🔍 **Current Implementation Status**

### **✅ Working Features:**
- Session management (create/start/stop/end)
- Real-time camera feed in frontend
- AI engine receives frames from frontend
- Face detection using OpenCV Haar Cascades
- WebSocket broadcasting of engagement data
- Redis caching for performance
- Retro-cyberpunk UI with live metrics
- Docker-based PostgreSQL + Redis setup

### **🔧 Technical Details:**
- **Frame Processing**: Frontend captures frames → Backend forwards to AI engine → AI processes with OpenCV → Results sent back via WebSocket
- **Performance**: 30 FPS processing capability with Redis caching
- **Scalability**: Microservices architecture with independent scaling
- **Reliability**: Graceful degradation when services unavailable

### **🎯 Key Differentiators:**
- **No Direct Camera Access**: AI engine processes frames from frontend (avoids camera conflicts)
- **Real-time Pipeline**: Sub-second latency from capture to display
- **Hybrid Storage**: Database + Redis + In-memory fallbacks
- **Modern Stack**: Go + React + Python with Docker orchestration

This system provides a complete real-time classroom engagement analytics solution with modern web technologies and AI processing capabilities! 🎯✨