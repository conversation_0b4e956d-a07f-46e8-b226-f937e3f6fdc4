@echo off
echo ========================================
echo    Ctrl-Vibe Minimal Setup (No Databases)
echo ========================================
echo.

REM Kill existing processes
echo [0/3] Cleaning up existing processes...
for /f "tokens=5" %%a in ('netstat -aon ^| findstr :5173') do taskkill /f /pid %%a >nul 2>&1
for /f "tokens=5" %%a in ('netstat -aon ^| findstr :8080') do taskkill /f /pid %%a >nul 2>&1
for /f "tokens=5" %%a in ('netstat -aon ^| findstr :8001') do taskkill /f /pid %%a >nul 2>&1
echo ✅ Existing processes cleaned

REM Start Python backend (no databases)
echo [1/3] Starting Python backend (memory-only)...
start "Python Backend" cmd /k "cd backend-python && pip install -r requirements.txt && set DISABLE_DATABASE=true && python app.py"
timeout /t 5 /nobreak >nul

REM Start AI engine
echo [2/3] Starting AI engine...
start "AI Engine" cmd /k "cd ai-engine && pip install -r requirements.txt && python src/main_simple.py"
timeout /t 3 /nobreak >nul

REM Start frontend
echo [3/3] Starting frontend...
start "Frontend" cmd /k "cd frontend && npm install && npm run dev"

echo.
echo ========================================
echo    🚀 Ctrl-Vibe Minimal System Ready!
echo ========================================
echo.
echo Services:
echo   • Python Backend: http://localhost:8080 (Memory only)
echo   • AI Engine: http://localhost:8001
echo   • Frontend: http://localhost:5173
echo.
echo Note: No databases - sessions stored in memory only
echo.
echo WORKFLOW:
echo   1. Open: http://localhost:5173
echo   2. Click "Start New Session"
echo   3. Click "Start Camera" to begin AI processing
echo.
pause
