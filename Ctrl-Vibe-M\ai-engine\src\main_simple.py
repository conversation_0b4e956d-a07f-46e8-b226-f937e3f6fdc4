#!/usr/bin/env python3
import cv2
import time
import logging
import threading
import sys
import os
import json
import numpy as np
from pathlib import Path

# Simple WebSocket client
class SimpleBackendClient:
    def __init__(self):
        self.backend_url = "http://localhost:8080"
        
    def send_data(self, data):
        try:
            import requests
            url = self.backend_url + "/api/ai/data"
            response = requests.post(url, json=data, timeout=2)
            return response.status_code == 200
        except:
            return False

# Logging configuration
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class SimpleEngagementSystem:
    """Simple engagement system that processes frames from frontend"""

    def __init__(self):
        self.session_id = None
        self.running = False
        self.backend_client = SimpleBackendClient()
        self.frame_count = 0
        
        logger.info("AI Engine initialized - waiting for session start")

    def start_session(self, session_id):
        """Start processing for a specific session"""
        self.session_id = session_id
        self.running = True
        logger.info("Starting session {} - ready to process frames".format(session_id))
        return True

    def process_frame(self, frame_data):
        """Process a single frame received from frontend"""
        try:
            # Decode frame from bytes
            nparr = np.frombuffer(frame_data, np.uint8)
            frame = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
            
            if frame is None:
                logger.warning("Failed to decode frame")
                return
            
            # Simple face detection using OpenCV
            gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
            face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')
            faces = face_cascade.detectMultiScale(gray, 1.1, 4)
            
            # Basic engagement data
            engagement_data = {
                'session_id': self.session_id,
                'timestamp': time.time(),
                'face_count': len(faces),
                'overall_engagement_score': min(len(faces) * 20, 100),
                'engagement_level': 'high' if len(faces) > 2 else 'medium' if len(faces) > 0 else 'low',
                'audio_level': 0.5,
                'gesture_count': len(faces),
                'alerts': []
            }
            
            # Send to backend
            self.backend_client.send_data(engagement_data)
            
            self.frame_count += 1
            
            if self.frame_count % 10 == 0:  # Log every 10 frames
                logger.info("Processed {} frames, detected {} faces".format(self.frame_count, len(faces)))
                
        except Exception as e:
            logger.error("Error processing frame: {}".format(e))

    def stop_session(self):
        """Stop the engagement analysis session"""
        logger.info("Stopping session")
        self.running = False
        self.session_id = None
        logger.info("Session stopped")

    def wait_for_commands(self):
        """Wait for HTTP commands from backend"""
        import requests
        from flask import Flask, request, jsonify
        
        app = Flask(__name__)
        
        @app.route('/start', methods=['POST'])
        def start_camera():
            data = request.get_json()
            session_id = data.get('session_id')
            if session_id:
                success = self.start_session(session_id)
                return jsonify({'status': 'started' if success else 'failed'})
            return jsonify({'error': 'No session_id provided'}), 400
        
        @app.route('/stop', methods=['POST'])
        def stop_camera():
            self.stop_session()
            return jsonify({'status': 'stopped'})
        
        @app.route('/process', methods=['POST'])
        def process_frame():
            if not self.running or not self.session_id:
                return jsonify({'error': 'Session not active'}), 400
            
            frame_data = request.get_data()
            if frame_data:
                self.process_frame(frame_data)
                return jsonify({'status': 'processed'})
            return jsonify({'error': 'No frame data'}), 400
        
        logger.info("AI Engine HTTP server starting on port 8001")
        app.run(host='0.0.0.0', port=8001, debug=False)

def main():
    """Main function - wait for commands"""
    logger.info("Starting Ctrl-Vibe AI Engine - Frame Processing Mode")

    try:
        system = SimpleEngagementSystem()
        system.wait_for_commands()
            
    except Exception as e:
        logger.error("Error starting AI Engine: {}".format(e))
    finally:
        if 'system' in locals():
            system.stop_session()
        logger.info("AI Engine shutdown complete")

if __name__ == "__main__":
    main()