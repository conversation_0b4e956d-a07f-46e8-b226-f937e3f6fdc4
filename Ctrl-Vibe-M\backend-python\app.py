#!/usr/bin/env python3
"""
Python Flask Backend for Ctrl-Vibe
Alternative to Go backend for users without Go installed
"""

from flask import Flask, request, jsonify
from flask_cors import CORS
import json
import time
import threading
import requests
from datetime import datetime
import os

app = Flask(__name__)
CORS(app, origins="*")

# Setup logging
import logging
logging.basicConfig(level=logging.INFO)

# In-memory storage (replace with database in production)
sessions = {}
session_counter = 1
active_sessions = set()
latest_engagement_data = {}  # Store latest engagement data per session

# AI Engine URL
AI_ENGINE_URL = "http://localhost:8001"

@app.route('/api/sessions', methods=['POST'])
def create_session():
    global session_counter
    
    data = request.get_json()
    session_name = data.get('name', f'Session {session_counter}')
    
    session = {
        'id': session_counter,
        'name': session_name,
        'start_time': datetime.now().isoformat(),
        'end_time': None,
        'is_active': True
    }
    
    sessions[session_counter] = session
    session_counter += 1
    
    return jsonify(session), 201

@app.route('/api/sessions', methods=['GET'])
def get_sessions():
    return jsonify(list(sessions.values()))

@app.route('/api/sessions/<int:session_id>', methods=['GET'])
def get_session(session_id):
    session = sessions.get(session_id)
    if not session:
        return jsonify({'error': 'Session not found'}), 404
    return jsonify(session)

@app.route('/api/sessions/<int:session_id>/end', methods=['PUT'])
def end_session(session_id):
    session = sessions.get(session_id)
    if not session:
        return jsonify({'error': 'Session not found'}), 404
    
    session['end_time'] = datetime.now().isoformat()
    session['is_active'] = False
    active_sessions.discard(session_id)
    
    return jsonify(session)

@app.route('/api/sessions/<int:session_id>/start-camera', methods=['POST'])
def start_camera(session_id):
    if session_id not in sessions:
        return jsonify({'error': 'Session not found'}), 404
    
    active_sessions.add(session_id)
    
    # Start AI engine
    def start_ai_engine():
        try:
            response = requests.post(f"{AI_ENGINE_URL}/start", 
                                   json={'session_id': session_id}, 
                                   timeout=5)
            print(f"AI engine started for session {session_id}")
        except Exception as e:
            print(f"Failed to start AI engine: {e}")
    
    threading.Thread(target=start_ai_engine, daemon=True).start()
    
    return jsonify({'status': 'camera_started', 'session_id': session_id})

@app.route('/api/sessions/<int:session_id>/stop-camera', methods=['POST'])
def stop_camera(session_id):
    active_sessions.discard(session_id)
    
    # Stop AI engine
    def stop_ai_engine():
        try:
            response = requests.post(f"{AI_ENGINE_URL}/stop", 
                                   json={'session_id': session_id}, 
                                   timeout=5)
            print(f"AI engine stopped for session {session_id}")
        except Exception as e:
            print(f"Failed to stop AI engine: {e}")
    
    threading.Thread(target=stop_ai_engine, daemon=True).start()
    
    return jsonify({'status': 'camera_stopped'})

@app.route('/api/sessions/<int:session_id>/process-frame', methods=['POST'])
def process_frame(session_id):
    if session_id not in active_sessions:
        return jsonify({'error': 'Session not active'}), 400
    
    frame_data = request.get_data()
    
    # Send frame to AI engine
    def send_to_ai():
        try:
            response = requests.post(f"{AI_ENGINE_URL}/process", 
                                   data=frame_data,
                                   headers={'Content-Type': 'application/octet-stream'},
                                   timeout=5)
        except Exception as e:
            print(f"Failed to send frame to AI engine: {e}")
    
    threading.Thread(target=send_to_ai, daemon=True).start()
    
    return jsonify({'status': 'frame_sent_for_processing'})

@app.route('/api/ai/data', methods=['POST'])
def receive_ai_data():
    """Receive engagement data from AI engine and store it"""
    ai_data = request.get_json()

    # Add timestamp if not present
    if 'timestamp' not in ai_data:
        ai_data['timestamp'] = time.time()

    # Store latest engagement data for this session
    session_id = ai_data.get('session_id')
    if session_id:
        latest_engagement_data[session_id] = ai_data
        print(f"Received engagement data for session {session_id}: {ai_data}")

    return jsonify({'status': 'success'})

@app.route('/api/sessions/<int:session_id>/metrics', methods=['GET'])
def get_session_metrics(session_id):
    # Return latest engagement data for this session
    if session_id in latest_engagement_data:
        return jsonify([latest_engagement_data[session_id]])
    return jsonify([])

@app.route('/api/sessions/<int:session_id>/live-data', methods=['GET'])
def get_live_data(session_id):
    """Get latest engagement data for real-time updates"""
    if session_id in latest_engagement_data:
        data = latest_engagement_data[session_id].copy()
        data['timestamp'] = time.time() * 1000  # Convert to milliseconds for frontend
        return jsonify(data)
    return jsonify({'session_id': session_id, 'timestamp': time.time() * 1000})

if __name__ == '__main__':
    print("Starting Ctrl-Vibe Python Backend...")
    print("Backend API: http://localhost:8080")
    print("Real-time data available via polling endpoints")
    app.run(host='0.0.0.0', port=8080, debug=False)
