package main

import (
	"ctrl-vibe-backend/internal/config"
	"ctrl-vibe-backend/internal/handlers"
	"ctrl-vibe-backend/internal/websocket"
	"ctrl-vibe-backend/pkg/database"
	"ctrl-vibe-backend/pkg/cache"
	"log"
	"os"
)

func main() {
	// Load configuration
	cfg := config.Load()

	// Skip database in development mode
	if os.Getenv("DISABLE_DATABASE") == "true" {
		log.Println("Running in development mode - database disabled")
	} else if cfg.DatabaseURL != "" {
		if err := database.Connect(cfg.DatabaseURL); err != nil {
			log.Printf("Database connection failed: %v", err)
			log.Println("Continuing without database...")
		}
	}

	// Initialize Redis
	if os.Getenv("DISABLE_REDIS") == "true" {
		log.Println("Redis disabled for development")
	} else {
		redisURL := os.Getenv("REDIS_URL")
		if redisURL != "" {
			if err := cache.InitRedis(redisURL); err != nil {
				log.Printf("Redis connection failed: %v", err)
				log.Println("Continuing without Redis...")
			} else {
				log.Println("Redis connected successfully")
			}
		}
	}

	// Create WebSocket hub
	hub := websocket.NewHub()
	go hub.Run()

	// Setup routes
	r := handlers.SetupRoutes(hub)

	// Start server
	log.Printf("Server starting on port %s", cfg.Port)
	if err := r.Run(":" + cfg.Port); err != nil {
		log.Fatal("Failed to start server:", err)
	}
}
