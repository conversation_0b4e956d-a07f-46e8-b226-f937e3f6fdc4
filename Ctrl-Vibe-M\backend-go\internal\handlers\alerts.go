package handlers

import (
	"ctrl-vibe-backend/internal/models"
	"ctrl-vibe-backend/pkg/database"
	"net/http"
	"github.com/gin-gonic/gin"
)

func GetSessionAlerts(c *gin.Context) {
	sessionID := c.Param("id")
	
	var alerts []models.Alert
	if err := database.DB.Where("session_id = ?", sessionID).Order("timestamp desc").Find(&alerts).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch alerts"})
		return
	}

	c.JSON(http.StatusOK, alerts)
}

func ResolveAlert(c *gin.Context) {
	id := c.Param("id")
	var alert models.Alert
	
	if err := database.DB.First(&alert, id).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Alert not found"})
		return
	}

	alert.IsResolved = true
	if err := database.DB.Save(&alert).Error; err != nil {
		c.JSO<PERSON>(http.StatusInternalServerError, gin.H{"error": "Failed to resolve alert"})
		return
	}

	c.J<PERSON>N(http.StatusOK, alert)
}
