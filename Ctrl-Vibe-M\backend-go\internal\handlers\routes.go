package handlers

import (
	"ctrl-vibe-backend/internal/websocket"
	"net/http"

	"github.com/gin-gonic/gin"
)

func SetupRoutes(hub *websocket.Hub) *gin.Engine {
	r := gin.Default()

	// CORS middleware
	r.Use(func(c *gin.Context) {
		c.<PERSON><PERSON>("Access-Control-Allow-Origin", "*")
		c.<PERSON>("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.<PERSON><PERSON>("Access-Control-Allow-Headers", "Content-Type, Authorization")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(204)
			return
		}

		c.Next()
	})

	// WebSocket endpoint
	r.GET("/ws", gin.WrapH(http.HandlerFunc(hub.HandleWebSocket)))

	// API routes
	api := r.Group("/api")
	{
		// Session routes
		api.POST("/sessions", CreateSession)
		api.GET("/sessions", GetSessions)
		api.GET("/sessions/:id", GetSession)
		api.PUT("/sessions/:id/end", EndSession)

		// Metrics routes
		metricsHandler := NewMetricsHandler(hub)
		api.POST("/ai/data", metricsHandler.ReceiveAIData)
		api.GET("/sessions/:id/metrics", metricsHandler.GetSessionMetrics)
		api.POST("/sessions/:id/start-camera", metricsHandler.StartCamera)
		api.POST("/sessions/:id/stop-camera", metricsHandler.StopCamera)
		api.POST("/sessions/:id/process-frame", metricsHandler.ProcessFrame)

		// Alerts routes
		api.GET("/sessions/:id/alerts", GetSessionAlerts)
		api.PUT("/alerts/:id/resolve", ResolveAlert)
	}

	return r
}
