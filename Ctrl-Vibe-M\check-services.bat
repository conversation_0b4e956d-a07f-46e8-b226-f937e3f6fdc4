@echo off
echo ========================================
echo    🔍 CTRL-VIBE SERVICE STATUS CHECK
echo ========================================
echo.

echo Checking if all services are running...
echo.

REM Check Frontend (port 5173)
echo 🔍 Checking Frontend (port 5173)...
netstat -an | findstr :5173 >nul
if %errorlevel%==0 (
    echo ✅ Frontend is running on port 5173
) else (
    echo ❌ Frontend is NOT running on port 5173
)

REM Check Backend (port 8080)
echo 🔍 Checking Backend (port 8080)...
netstat -an | findstr :8080 >nul
if %errorlevel%==0 (
    echo ✅ Backend is running on port 8080
) else (
    echo ❌ Backend is NOT running on port 8080
)

REM Check AI Engine (port 8001)
echo 🔍 Checking AI Engine (port 8001)...
netstat -an | findstr :8001 >nul
if %errorlevel%==0 (
    echo ✅ AI Engine is running on port 8001
) else (
    echo ❌ AI Engine is NOT running on port 8001
)

echo.
echo ========================================
echo    🌐 QUICK ACCESS LINKS
echo ========================================
echo.
echo 🎮 Frontend Dashboard: http://localhost:5173
echo 🐍 Backend API Status: http://localhost:8080/api/sessions
echo 🤖 AI Engine Status: http://localhost:8001
echo.
echo 💡 If any service shows as NOT running:
echo    1. Check the service windows for error messages
echo    2. Try running start-all-python.bat again
echo    3. Make sure no other apps are using these ports
echo.
pause
