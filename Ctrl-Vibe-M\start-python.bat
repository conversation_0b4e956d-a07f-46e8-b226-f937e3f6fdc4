@echo off
echo ========================================
echo    Ctrl-Vibe Python-Only Deployment
echo ========================================
echo.

REM Kill existing processes
echo [0/4] Cleaning up existing processes...
for /f "tokens=5" %%a in ('netstat -aon ^| findstr :5173') do taskkill /f /pid %%a >nul 2>&1
for /f "tokens=5" %%a in ('netstat -aon ^| findstr :8080') do taskkill /f /pid %%a >nul 2>&1
for /f "tokens=5" %%a in ('netstat -aon ^| findstr :8001') do taskkill /f /pid %%a >nul 2>&1
echo ✅ Existing processes cleaned

REM Start PostgreSQL and Redis (optional)
echo [1/4] Starting PostgreSQL and Redis...
docker-compose -f docker-compose.dev.yml up postgres redis -d
timeout /t 5 /nobreak >nul

REM Start Python backend
echo [2/4] Starting Python backend...
start "Python Backend" cmd /k "cd backend-python && pip install -r requirements.txt && python app.py"
timeout /t 5 /nobreak >nul

REM Start AI engine
echo [3/4] Starting AI engine...
start "AI Engine" cmd /k "cd ai-engine && pip install -r requirements.txt && python src/main_simple.py"
timeout /t 3 /nobreak >nul

REM Start frontend
echo [4/4] Starting frontend...
start "Frontend" cmd /k "cd frontend && npm install && npm run dev"

echo.
echo ========================================
echo    🚀 Ctrl-Vibe System Ready!
echo ========================================
echo.
echo Services:
echo   • PostgreSQL: localhost:5432 (Docker)
echo   • Redis: localhost:6379 (Docker)
echo   • Python Backend: http://localhost:8080
echo   • AI Engine: http://localhost:8001
echo   • Frontend: http://localhost:5173
echo.
echo WORKFLOW:
echo   1. Open: http://localhost:5173
echo   2. Click "Start New Session"
echo   3. Click "Start Camera" to begin AI processing
echo.
pause
