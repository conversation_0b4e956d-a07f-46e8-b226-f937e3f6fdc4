import React, { useState, useEffect } from 'react'
import { EngagementData, Alert } from '../types'
import { PollingService } from '../services/polling'
import {
  Users, Brain, Eye, Monitor, BarChart3, Settings, Target
} from 'lucide-react'
import CameraControl from '../components/shared/CameraControl'
import CameraFeed from '../components/shared/CameraFeed'

const Dashboard: React.FC = () => {
  const [currentData, setCurrentData] = useState<EngagementData | null>(null)
  const [historicalData, setHistoricalData] = useState<EngagementData[]>([])
  const [alerts, setAlerts] = useState<Alert[]>([])
  const [pollingService] = useState(() => new PollingService())
  const [currentSessionId, setCurrentSessionId] = useState<number | null>(null)

  useEffect(() => {
    if (currentSessionId) {
      pollingService.connect((data: EngagementData) => {
        if (data.session_id === currentSessionId) {
          setCurrentData(data)
          setHistoricalData(prev => [...prev.slice(-19), data])
          if (data.alerts && data.alerts.length > 0) {
            setAlerts(prev => [...data.alerts, ...prev].slice(0, 10))
          }
        }
      }, currentSessionId)
    }
    return () => pollingService.disconnect()
  }, [pollingService, currentSessionId])

  const handleSessionStart = (sessionId: number) => {
    setCurrentSessionId(sessionId)
    setCurrentData(null)
    setHistoricalData([])
    setAlerts([])
  }

  const handleSessionStop = () => {
    setCurrentSessionId(null)
  }

  const getEngagementColor = (level: string) => {
    switch (level) {
      case 'high': return 'text-green-600 bg-green-100'
      case 'medium': return 'text-yellow-600 bg-yellow-100'
      case 'low': return 'text-red-600 bg-red-100'
      default: return 'text-gray-600 bg-gray-100'
    }
  }

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'error': return 'text-red-600 bg-red-100'
      case 'warning': return 'text-yellow-600 bg-yellow-100'
      case 'info': return 'text-blue-600 bg-blue-100'
      default: return 'text-gray-600 bg-gray-100'
    }
  }

  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId)
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' })
    }
  }

  return (
    <div className="min-h-screen relative">
      {/* Navigation Bar */}
      <nav className="fixed top-0 w-full bg-gray-900/80 backdrop-blur-lg border-b border-purple-500/30 z-50">
        <div className="max-w-7xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg flex items-center justify-center">
                <Brain className="w-6 h-6 text-white" />
              </div>
              <span className="text-pixel-lg font-bold bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent text-shadow-pixel">
                CTRL-VIBE
              </span>
            </div>
            <div className="hidden md:flex space-x-6">
              {[
                { name: 'Dashboard', id: 'dashboard' },
                { name: 'Analytics', id: 'analytics' },
                { name: 'Live Feed', id: 'live-feed' },
                { name: 'Settings', id: 'settings' }
              ].map((item) => (
                <button
                  key={item.id}
                  onClick={() => scrollToSection(item.id)}
                  className="px-4 py-2 text-gray-300 hover:text-white hover:bg-purple-500/20 rounded-lg transition-all duration-200 font-pixel text-xs"
                >
                  {item.name.toUpperCase()}
                </button>
              ))}
            </div>
          </div>
        </div>
      </nav>

      {/* Main Dashboard */}
      <section id="dashboard" className="pt-24 pb-16 px-6">
        <div className="max-w-7xl mx-auto">
          {/* Header */}
          <div className="text-center mb-12">
            <h1 className="text-pixel-2xl lg:text-pixel-xl font-bold mb-8 leading-relaxed text-shadow-pixel">
              <span className="gradient-text block mb-4 font-cyber">
                CLASSROOM ENGAGEMENT
              </span>
              <span className="text-white font-pixel">ANALYTICS DASHBOARD</span>
            </h1>
            <p className="text-gray-400 font-retro text-lg max-w-4xl mx-auto leading-relaxed">
              REAL-TIME AI-POWERED ANALYSIS OF STUDENT ENGAGEMENT, ATTENTION, AND PARTICIPATION USING ADVANCED COMPUTER VISION AND MACHINE LEARNING
            </p>
          </div>

          {/* Session Control Card - Centered */}
          <div className="flex justify-center mb-12">
            <div className="bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-lg border border-purple-500/30 rounded-2xl p-8 max-w-md w-full">
              <div className="text-center mb-6">
                <div className="w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Eye className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-pixel-lg font-bold text-white mb-3 text-shadow-pixel">SESSION CONTROL</h3>
                <p className="text-gray-400 font-retro text-sm">START OR STOP YOUR ENGAGEMENT ANALYSIS SESSION</p>
              </div>
              <CameraControl
                sessionId={currentSessionId}
                onSessionStart={handleSessionStart}
                onSessionStop={handleSessionStop}
              />
            </div>
          </div>

          {/* Live Analytics Grid */}
          {currentSessionId && (
            <div className="grid lg:grid-cols-3 gap-8 mb-12">
              {/* Camera Feed - Takes 2 columns */}
              <div className="lg:col-span-2">
                <div className="bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-lg border border-cyan-500/30 rounded-2xl p-6 h-full">
                  <div className="flex items-center justify-between mb-6">
                    <h3 className="text-pixel-lg font-bold text-white flex items-center text-shadow-pixel">
                      <Monitor className="w-6 h-6 mr-3 text-cyan-400" />
                      LIVE CAMERA FEED
                    </h3>
                    <div className="flex items-center space-x-2">
                      <div className="w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
                      <span className="text-green-400 font-pixel text-xs font-medium">LIVE</span>
                    </div>
                  </div>
                  <div className="bg-black/50 rounded-xl overflow-hidden border border-gray-700">
                    <CameraFeed
                      sessionId={currentSessionId}
                      isActive={!!currentSessionId}
                      engagementData={currentData ? {
                        face_count: currentData.face_count,
                        overall_engagement_score: currentData.overall_engagement_score,
                        engagement_level: currentData.engagement_level
                      } : undefined}
                    />
                  </div>
                </div>
              </div>

              {/* Metrics Panel */}
              <div className="space-y-6">
                {/* Real-time Metrics */}
                <div className="bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-lg border border-purple-500/30 rounded-2xl p-6">
                  <h3 className="text-pixel font-bold text-white mb-4 flex items-center text-shadow-pixel">
                    <BarChart3 className="w-5 h-5 mr-2 text-purple-400" />
                    LIVE METRICS
                  </h3>
                  <div className="space-y-4">
                    <div className="bg-gradient-to-r from-purple-500/20 to-pink-500/20 border border-purple-500/30 rounded-xl p-4">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center">
                          <Users className="w-5 h-5 text-purple-400 mr-3" />
                          <span className="text-gray-300 font-pixel text-xs">STUDENTS</span>
                        </div>
                        <span className="text-pixel-xl font-bold text-purple-400">{currentData?.face_count || 0}</span>
                      </div>
                    </div>

                    <div className="bg-gradient-to-r from-cyan-500/20 to-blue-500/20 border border-cyan-500/30 rounded-xl p-4">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center">
                          <Target className="w-5 h-5 text-cyan-400 mr-3" />
                          <span className="text-gray-300 font-pixel text-xs">ENGAGEMENT</span>
                        </div>
                        <span className="text-pixel-xl font-bold text-cyan-400">
                          {currentData?.overall_engagement_score?.toFixed(0) || 0}%
                        </span>
                      </div>
                    </div>

                    <div className="bg-gradient-to-r from-pink-500/20 to-rose-500/20 border border-pink-500/30 rounded-xl p-4">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center">
                          <Eye className="w-5 h-5 text-pink-400 mr-3" />
                          <span className="text-gray-300 font-pixel text-xs">ATTENTION</span>
                        </div>
                        <span className="text-pixel-xl font-bold text-pink-400">
                          {currentData?.engagement_level || 'Low'}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Status Panel */}
                <div className="bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-lg border border-green-500/30 rounded-2xl p-6">
                  <h3 className="text-pixel font-bold text-white mb-4 flex items-center text-shadow-pixel">
                    <Settings className="w-5 h-5 mr-2 text-green-400" />
                    SYSTEM STATUS
                  </h3>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span className="text-gray-300 font-pixel text-xs">AI PROCESSING</span>
                      <div className="flex items-center">
                        <div className="w-2 h-2 bg-green-400 rounded-full mr-2 animate-pulse"></div>
                        <span className="text-green-400 font-pixel text-xs">ACTIVE</span>
                      </div>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-gray-300 font-pixel text-xs">CAMERA FEED</span>
                      <div className="flex items-center">
                        <div className="w-2 h-2 bg-green-400 rounded-full mr-2 animate-pulse"></div>
                        <span className="text-green-400 font-pixel text-xs">CONNECTED</span>
                      </div>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-gray-300 font-pixel text-xs">POLLING</span>
                      <div className="flex items-center">
                        <div className="w-2 h-2 bg-green-400 rounded-full mr-2 animate-pulse"></div>
                        <span className="text-green-400 font-pixel text-xs">ACTIVE</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* No Session State */}
          {!currentSessionId && (
            <div className="text-center py-20">
              <div className="bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-lg border border-purple-500/30 rounded-2xl p-12 max-w-2xl mx-auto">
                <div className="w-20 h-20 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center mx-auto mb-6">
                  <Brain className="w-10 h-10 text-white" />
                </div>
                <h2 className="text-pixel-xl font-bold text-white mb-6 text-shadow-pixel">READY TO ANALYZE</h2>
                <p className="text-gray-400 font-retro text-base mb-8 max-w-2xl mx-auto">
                  START A NEW SESSION TO BEGIN REAL-TIME CLASSROOM ENGAGEMENT ANALYSIS WITH OUR ADVANCED AI SYSTEM.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <button className="btn-primary font-pixel text-xs">
                    START NEW SESSION
                  </button>
                  <button className="btn-secondary font-pixel text-xs">
                    VIEW DOCUMENTATION
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      </section>

      {/* Analytics Section */}
      <section id="analytics" className="py-16 px-6">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-pixel-xl lg:text-pixel-2xl font-bold mb-6 text-shadow-pixel">
              <span className="gradient-text-alt font-cyber">
                ADVANCED ANALYTICS
              </span>
            </h2>
            <p className="text-gray-400 font-retro text-lg max-w-4xl mx-auto leading-relaxed">
              COMPREHENSIVE INSIGHTS INTO CLASSROOM ENGAGEMENT PATTERNS AND STUDENT BEHAVIOR WITH REAL-TIME PROCESSING
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            {/* Feature Cards */}
            <div className="bg-gradient-to-br from-purple-500/10 to-pink-500/10 border border-purple-500/30 rounded-2xl p-8 hover:scale-105 transition-transform duration-300">
              <div className="w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl flex items-center justify-center mb-6">
                <Eye className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-pixel-lg font-bold text-white mb-4 text-shadow-pixel">COMPUTER VISION</h3>
              <p className="text-gray-400 font-retro text-sm mb-6">
                ADVANCED FACIAL RECOGNITION AND EMOTION DETECTION WITH REAL-TIME ENGAGEMENT SCORING
                FOR COMPREHENSIVE CLASSROOM ANALYSIS.
              </p>
              <div className="text-purple-400 font-pixel text-xs font-semibold">REAL-TIME PROCESSING</div>
            </div>

            <div className="bg-gradient-to-br from-cyan-500/10 to-blue-500/10 border border-cyan-500/30 rounded-2xl p-8 hover:scale-105 transition-transform duration-300">
              <div className="w-16 h-16 bg-gradient-to-r from-cyan-500 to-blue-500 rounded-xl flex items-center justify-center mb-6">
                <Brain className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-pixel-lg font-bold text-white mb-4 text-shadow-pixel">AI ANALYTICS</h3>
              <p className="text-gray-400 font-retro text-sm mb-6">
                MACHINE LEARNING ALGORITHMS ANALYZE BEHAVIORAL PATTERNS AND PROVIDE
                ACTIONABLE INSIGHTS FOR IMPROVED TEACHING STRATEGIES.
              </p>
              <div className="text-cyan-400 font-pixel text-xs font-semibold">SMART INSIGHTS</div>
            </div>

            <div className="bg-gradient-to-br from-pink-500/10 to-rose-500/10 border border-pink-500/30 rounded-2xl p-8 hover:scale-105 transition-transform duration-300">
              <div className="w-16 h-16 bg-gradient-to-r from-pink-500 to-rose-500 rounded-xl flex items-center justify-center mb-6">
                <BarChart3 className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-pixel-lg font-bold text-white mb-4 text-shadow-pixel">LIVE REPORTING</h3>
              <p className="text-gray-400 font-retro text-sm mb-6">
                INSTANT FEEDBACK AND DETAILED REPORTS ON STUDENT ENGAGEMENT,
                ATTENTION LEVELS, AND PARTICIPATION METRICS.
              </p>
              <div className="text-pink-400 font-pixel text-xs font-semibold">INSTANT REPORTS</div>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="py-12 px-6 border-t border-gray-800">
        <div className="max-w-7xl mx-auto">
          <div className="text-center">
            <div className="flex items-center justify-center space-x-3 mb-6">
              <div className="w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg flex items-center justify-center">
                <Brain className="w-6 h-6 text-white" />
              </div>
              <span className="text-pixel-lg font-bold bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent text-shadow-pixel">
                CTRL-VIBE
              </span>
            </div>
            <p className="text-gray-400 font-retro text-sm mb-8 max-w-3xl mx-auto">
              REVOLUTIONIZING CLASSROOM ENGAGEMENT THROUGH AI-POWERED ANALYTICS AND REAL-TIME INSIGHTS.
            </p>
            <div className="flex flex-wrap justify-center gap-8 text-sm text-gray-500">
              <button className="hover:text-purple-400 transition-colors font-pixel text-xs">PRIVACY POLICY</button>
              <button className="hover:text-purple-400 transition-colors font-pixel text-xs">TERMS OF SERVICE</button>
              <button className="hover:text-purple-400 transition-colors font-pixel text-xs">DOCUMENTATION</button>
              <button className="hover:text-purple-400 transition-colors font-pixel text-xs">SUPPORT</button>
            </div>
            <div className="mt-8 pt-8 border-t border-gray-800">
              <p className="text-gray-500 font-pixel text-xs">
                © 2024 CTRL-VIBE. ALL RIGHTS RESERVED. BUILT WITH ❤️ FOR EDUCATORS.
              </p>
            </div>
          </div>
        </div>
      </footer>
    </div>
  )
}

export default Dashboard
