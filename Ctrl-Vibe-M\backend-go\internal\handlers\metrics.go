package handlers

import (
	"bytes"
	"ctrl-vibe-backend/internal/models"
	"ctrl-vibe-backend/internal/websocket"
	"ctrl-vibe-backend/pkg/cache"
	"ctrl-vibe-backend/pkg/database"
	"encoding/json"
	"fmt"
	"net/http"
	"os"
	"time"

	"github.com/gin-gonic/gin"
)

type MetricsHandler struct {
	hub *websocket.Hub
}

func NewMetricsHandler(hub *websocket.Hub) *MetricsHandler {
	return &MetricsHandler{hub: hub}
}

func (h *MetricsHandler) ReceiveAIData(c *gin.Context) {
	var aiData map[string]interface{}
	if err := c.ShouldBindJSON(&aiData); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Add timestamp if not present
	if _, exists := aiData["timestamp"]; !exists {
		aiData["timestamp"] = time.Now().Unix()
	}

	// Cache the data in Redis for 5 minutes
	sessionID := fmt.Sprintf("%v", aiData["session_id"])
	cacheKey := fmt.Sprintf("ai_data:%s", sessionID)
	cache.Set(cacheKey, aiData, 5*time.Minute)

	// Broadcast to WebSocket clients
	h.hub.BroadcastData(aiData)

	c.JSON(http.StatusOK, gin.H{"status": "success"})
}

func (h *MetricsHandler) StartCamera(c *gin.Context) {
	sessionID := c.Param("id")

	// Check if session exists in memory or database
	if os.Getenv("DISABLE_DATABASE") != "true" && database.DB != nil {
		var session models.Session
		if err := database.DB.First(&session, sessionID).Error; err != nil {
			c.JSON(http.StatusNotFound, gin.H{"error": "Session not found"})
			return
		}
		session.IsActive = true
		database.DB.Save(&session)
	}

	// Cache session status
	cache.Set(fmt.Sprintf("session:%s:active", sessionID), true, 1*time.Hour)

	// Trigger AI engine via HTTP
	go func() {
		time.Sleep(1 * time.Second) // Wait for response to be sent
		payload := map[string]interface{}{"session_id": sessionID}
		jsonData, _ := json.Marshal(payload)

		resp, err := http.Post("http://localhost:8001/start", "application/json", bytes.NewBuffer(jsonData))
		if err != nil {
			fmt.Printf("Failed to start AI engine: %v\n", err)
		} else {
			fmt.Printf("AI engine started for session %s\n", sessionID)
			resp.Body.Close()
		}
	}()

	c.JSON(http.StatusOK, gin.H{"status": "camera_started", "session_id": sessionID})
}

func (h *MetricsHandler) StopCamera(c *gin.Context) {
	sessionID := c.Param("id")

	// Remove from cache
	cache.Delete(fmt.Sprintf("session:%s:active", sessionID))

	// Signal AI engine to stop
	go func() {
		payload := map[string]interface{}{"session_id": sessionID}
		jsonData, _ := json.Marshal(payload)

		resp, err := http.Post("http://localhost:8001/stop", "application/json", bytes.NewBuffer(jsonData))
		if err == nil {
			resp.Body.Close()
		}
	}()

	c.JSON(http.StatusOK, gin.H{"status": "camera_stopped"})
}

func (h *MetricsHandler) ProcessFrame(c *gin.Context) {
	// Get the uploaded frame data
	frameData, err := c.GetRawData()
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Failed to read frame data"})
		return
	}

	// Send frame directly to AI engine
	go func() {
		resp, err := http.Post("http://localhost:8001/process", "application/octet-stream", bytes.NewBuffer(frameData))
		if err != nil {
			fmt.Printf("Failed to send frame to AI engine: %v\n", err)
		} else {
			resp.Body.Close()
		}
	}()

	c.JSON(http.StatusOK, gin.H{"status": "frame_sent_for_processing"})
}

func (h *MetricsHandler) GetSessionMetrics(c *gin.Context) {
	sessionID := c.Param("id")

	// Try to get from cache first
	cacheKey := fmt.Sprintf("ai_data:%s", sessionID)
	var cachedData map[string]interface{}
	if err := cache.Get(cacheKey, &cachedData); err == nil {
		c.JSON(http.StatusOK, []interface{}{cachedData})
		return
	}

	// Fallback to database if available
	if database.DB != nil {
		var metrics []models.SessionMetric
		if err := database.DB.Where("session_id = ?", sessionID).Order("timestamp desc").Limit(100).Find(&metrics).Error; err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch metrics"})
			return
		}
		c.JSON(http.StatusOK, metrics)
		return
	}

	c.JSON(http.StatusOK, []interface{}{})
}
