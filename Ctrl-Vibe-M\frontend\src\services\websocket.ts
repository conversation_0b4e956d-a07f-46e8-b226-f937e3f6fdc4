import { EngagementData } from '../types'
import { endpoints, config } from '../config/environment'

export class WebSocketService {
  private ws: WebSocket | null = null
  private socketIO: any = null
  private url: string
  private onDataCallback?: (data: EngagementData) => void
  private reconnectAttempts: number = 0
  private maxReconnectAttempts: number = 5
  private reconnectDelay: number = 3000
  private useSocketIO: boolean = true // Use Socket.IO for Python backend

  constructor(url?: string) {
    this.url = url || endpoints.ws
  }

  connect(onData: (data: EngagementData) => void) {
    this.onDataCallback = onData

    if (this.useSocketIO) {
      // Use Socket.IO for Python Flask-SocketIO backend
      this.connectSocketIO(onData)
    } else {
      // Use regular WebSocket for Go backend
      this.connectWebSocket(onData)
    }
  }

  private connectSocketIO(onData: (data: EngagementData) => void) {
    try {
      // Dynamically import socket.io-client if available, otherwise fallback to WebSocket
      if (typeof window !== 'undefined' && (window as any).io) {
        this.socketIO = (window as any).io('http://localhost:8080')

        this.socketIO.on('connect', () => {
          console.log('Socket.IO connected')
          this.reconnectAttempts = 0
        })

        this.socketIO.on('engagement_data', (data: EngagementData) => {
          data.timestamp = Date.now()
          this.onDataCallback?.(data)
        })

        this.socketIO.on('disconnect', () => {
          console.log('Socket.IO disconnected')
          if (this.reconnectAttempts < this.maxReconnectAttempts) {
            this.reconnectAttempts++
            setTimeout(() => this.connectSocketIO(onData), this.reconnectDelay)
          }
        })
      } else {
        // Fallback to WebSocket if Socket.IO not available
        console.log('Socket.IO not available, falling back to WebSocket')
        this.connectWebSocket(onData)
      }
    } catch (error) {
      console.error('Socket.IO connection failed, falling back to WebSocket:', error)
      this.connectWebSocket(onData)
    }
  }

  private connectWebSocket(onData: (data: EngagementData) => void) {
    try {
      this.ws = new WebSocket(this.url)

      this.ws.onopen = () => {
        console.log('WebSocket connected')
        this.reconnectAttempts = 0
      }

      this.ws.onmessage = (event) => {
        try {
          const data: EngagementData = JSON.parse(event.data)
          data.timestamp = Date.now()
          this.onDataCallback?.(data)
        } catch (error) {
          console.error('Error parsing WebSocket message:', error)
        }
      }

      this.ws.onclose = (event) => {
        console.log('WebSocket disconnected:', event.code, event.reason)

        // Only attempt reconnection in development or if it wasn't a clean close
        if (config.isDevelopment || (event.code !== 1000 && this.reconnectAttempts < this.maxReconnectAttempts)) {
          this.reconnectAttempts++
          console.log(`Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts})...`)
          setTimeout(() => this.connectWebSocket(onData), this.reconnectDelay)
        }
      }

      this.ws.onerror = (error) => {
        console.error('WebSocket error:', error)
      }
    } catch (error) {
      console.error('Failed to connect to WebSocket:', error)
    }
  }

  disconnect() {
    if (this.socketIO) {
      this.socketIO.disconnect()
      this.socketIO = null
    }
    if (this.ws) {
      this.ws.close(1000, 'User disconnected')
      this.ws = null
    }
    this.reconnectAttempts = 0
  }

  isConnected(): boolean {
    if (this.socketIO) {
      return this.socketIO.connected
    }
    return this.ws?.readyState === WebSocket.OPEN
  }
}
