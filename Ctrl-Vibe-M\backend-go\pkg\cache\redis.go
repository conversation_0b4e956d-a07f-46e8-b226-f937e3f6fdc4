package cache

import (
	"context"
	"encoding/json"
	"time"

	"github.com/redis/go-redis/v9"
)

var RedisClient *redis.Client

func InitRedis(redisURL string) error {
	opt, err := redis.ParseURL(redisURL)
	if err != nil {
		return err
	}

	RedisClient = redis.NewClient(opt)
	
	// Test connection
	ctx := context.Background()
	_, err = RedisClient.Ping(ctx).Result()
	return err
}

func Set(key string, value interface{}, expiration time.Duration) error {
	if RedisClient == nil {
		return nil // Skip if Redis not available
	}
	ctx := context.Background()
	json, err := json.Marshal(value)
	if err != nil {
		return err
	}
	return RedisClient.Set(ctx, key, json, expiration).Err()
}

func Get(key string, dest interface{}) error {
	if RedisClient == nil {
		return redis.Nil // Return not found if Redis not available
	}
	ctx := context.Background()
	val, err := RedisClient.Get(ctx, key).Result()
	if err != nil {
		return err
	}
	return json.Unmarshal([]byte(val), dest)
}

func Delete(key string) error {
	if RedisClient == nil {
		return nil // Skip if Redis not available
	}
	ctx := context.Background()
	return RedisClient.Del(ctx, key).Err()
}