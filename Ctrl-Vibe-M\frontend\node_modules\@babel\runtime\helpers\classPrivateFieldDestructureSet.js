var classApplyDescriptorDestructureSet = require("./classApplyDescriptorDestructureSet.js");
var classPrivateFieldGet2 = require("./classPrivateFieldGet2.js");
function _classPrivateFieldDestructureSet(e, t) {
  var r = classPrivateFieldGet2(t, e);
  return classApplyDescriptorDestructureSet(e, r);
}
module.exports = _classPrivateFieldDestructureSet, module.exports.__esModule = true, module.exports["default"] = module.exports;