#!/bin/bash

echo "========================================"
echo "    🚀 CTRL-VIBE ALL-IN-ONE STARTUP"
echo "    (Python Backend Version - No Go Required)"
echo "========================================"
echo ""

# Kill any existing processes on our ports (simplified for Git Bash)
echo "[CLEANUP] Stopping existing processes..."
echo "✅ Cleanup complete (manual port cleanup if needed)"

echo ""
echo "[STARTING] Launching all services..."
echo ""

# Start Python Backend in background
echo "🐍 Starting Python Backend on port 8080..."
cd backend-python
pip install flask flask-cors requests
echo "Backend starting..."
python app.py &
BACKEND_PID=$!
cd ..

# Wait 4 seconds for backend to initialize
echo "⏳ Waiting for backend to initialize..."
sleep 4

# Start AI Engine in background
echo "🤖 Starting AI Engine on port 8001..."
cd ai-engine
pip install opencv-python flask requests numpy
echo "AI Engine starting..."
python src/main_simple.py &
AI_PID=$!
cd ..

# Wait 3 seconds for AI engine to initialize
echo "⏳ Waiting for AI engine to initialize..."
sleep 3

# Start Frontend (this will run in foreground)
echo "⚛️ Starting React Frontend on port 5173..."
cd frontend
npm install
echo "Frontend starting..."
echo ""
echo "🎯 All services are starting! Frontend will open in this terminal."
echo "🌐 Open http://localhost:5173 in your browser"
echo ""
echo "🛑 Press Ctrl+C to stop all services"
echo ""

# Trap Ctrl+C to kill background processes
trap 'echo "Stopping all services..."; kill $BACKEND_PID $AI_PID 2>/dev/null; exit' INT

npm run dev

echo ""
echo "========================================"
echo "    ✨ ALL SERVICES LAUNCHED!"
echo "========================================"
echo ""
echo "🌐 Services will be available at:"
echo "   • 🎮 Frontend Dashboard: http://localhost:5173"
echo "   • 🐍 Python Backend API: http://localhost:8080"
echo "   • 🤖 AI Engine: http://localhost:8001"
echo ""
echo "📋 COMPLETE WORKFLOW:"
echo "   1. ⏳ Wait ~30-60 seconds for all services to fully start"
echo "   2. 🌐 Open: http://localhost:5173"
echo "   3. 📷 Click \"Grant Camera Permission\""
echo "   4. ▶️ Click \"Start New Session\""
echo "   5. 🎥 Click \"Start Camera\" to begin AI analysis"
echo "   6. 📊 Watch real-time engagement metrics!"
echo ""
echo "💡 MONITORING:"
echo "   • Each service runs in its own window"
echo "   • Watch for \"Server starting\" messages"
echo "   • Frontend will show \"Local: http://localhost:5173\""
echo "   • Backend will show \"Running on http://0.0.0.0:8080\""
echo "   • AI Engine will show \"AI Engine HTTP server starting\""
echo ""
echo "🛑 TO STOP: Close any service window to stop that service"
echo ""
echo "⚡ NO ENVIRONMENT VARIABLES NEEDED - Everything uses localhost defaults!"
echo ""
read -p "Press Enter to continue..."
