version: '3.8'

services:
  postgres:
    image: postgres:15
    container_name: ctrl-vibe-postgres
    environment:
      POSTGRES_DB: ctrl_vibe
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5

  redis:
    image: redis:7-alpine
    container_name: ctrl-vibe-redis
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  backend:
    build:
      context: ./backend-go
      dockerfile: Dockerfile
    container_name: ctrl-vibe-backend
    ports:
      - "8080:8080"
    environment:
      - DATABASE_URL=******************************************/ctrl_vibe?sslmode=disable
      - REDIS_URL=redis://redis:6379
      - PORT=8080
      - DISABLE_DATABASE=false
      - DISABLE_REDIS=false
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped

  ai-engine:
    build:
      context: ./ai-engine
      dockerfile: Dockerfile
    container_name: ctrl-vibe-ai
    ports:
      - "8001:8001"
    environment:
      - BACKEND_URL=http://backend:8080
    depends_on:
      - backend
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data: