@echo off
echo ========================================
echo    Ctrl-Vibe Docker Deployment
echo ========================================
echo.

REM Kill existing processes on ports
echo [1/4] Cleaning up existing processes...
for /f "tokens=5" %%a in ('netstat -aon ^| findstr :5173') do taskkill /f /pid %%a >nul 2>&1
for /f "tokens=5" %%a in ('netstat -aon ^| findstr :8080') do taskkill /f /pid %%a >nul 2>&1
for /f "tokens=5" %%a in ('netstat -aon ^| findstr :8001') do taskkill /f /pid %%a >nul 2>&1
echo ✅ Existing processes cleaned

REM Stop any existing containers
echo [2/4] Stopping existing containers...
docker-compose -f docker-compose.dev.yml down
echo ✅ Containers stopped

REM Build and start all services
echo [3/4] Building and starting all services...
docker-compose -f docker-compose.dev.yml up --build -d
echo ✅ Backend services started

REM Wait for services to be ready
echo Waiting for services to start...
timeout /t 10 /nobreak >nul

REM Start frontend
echo [4/4] Starting frontend...
start "Frontend" cmd /k "cd frontend && npm install && npm run dev"

echo.
echo ========================================
echo    🚀 Ctrl-Vibe System Ready!
echo ========================================
echo.
echo Services:
echo   • PostgreSQL: localhost:5432
echo   • Redis: localhost:6379
echo   • Backend: http://localhost:8080 (Docker)
echo   • AI Engine: http://localhost:8001 (Docker)
echo   • Frontend: http://localhost:5173
echo.
echo WORKFLOW:
echo   1. Open: http://localhost:5173
echo   2. Click "Start New Session"
echo   3. Click "Start Camera" to begin AI processing
echo.
echo To stop all services: docker-compose -f docker-compose.dev.yml down
echo.
pause
